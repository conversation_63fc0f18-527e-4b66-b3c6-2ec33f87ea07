import React from "react";
import { Card, Col, Row } from "antd";
 
import TodoManagement from './TodoManagement';
import TeamListCard from './TeamListCard';
import UserProfileCard from "./UserProfileCard";

const FleetManagementDashboard: React.FC = () => {
  return (
    <div style={{ minHeight: "100vh", background: "#f5f8ff", padding: "24px" }}>
      {/* 大的容器区域 */}
      <Card
        style={{
          width: "100%",
          minHeight: "calc(100vh - 48px)",
          borderRadius: "12px",
          boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)"
        }}
        styles={{ body: { padding: "24px" } }}
      >
        <Row gutter={[24, 24]}>
          {/* 个人信息卡片 */}
          <Col xs={24}>
            <UserProfileCard />
          </Col>

          {/* 待办事项 */}
          <Col xs={24} lg={12}>
            <TodoManagement />
          </Col>

          {/* 团队列表 */}
          <Col xs={24} lg={12}>
            <TeamListCard />
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default FleetManagementDashboard;