import {
  UserOutlined,
} from '@ant-design/icons';
import { history, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Spin, Avatar, Typography } from 'antd';
import { createStyles } from 'antd-style';
import React from 'react';
import HeaderDropdown from '../HeaderDropdown';

const { Text } = Typography;

export type GlobalHeaderRightProps = {
  menu?: boolean;
  children?: React.ReactNode;
};

export const AvatarName = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};
  return <span className="anticon">{currentUser?.name}</span>;
};

const useStyles = createStyles(({ token }) => {
  return {
    action: {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    },
    dropdownItem: {
      display: 'flex',
      alignItems: 'center',
      gap: 8,
      padding: '8px 12px',
      minWidth: 200,
    },
  };
});

export const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({
  menu,
  children,
}) => {
  const { styles } = useStyles();
  const { initialState } = useModel('@@initialState');

  const onMenuClick: MenuProps['onClick'] = async (event) => {
    const { key } = event;

    // 处理菜单项点击
    switch (key) {
      case 'profile':
        history.push('/personal-center');
        break;
      default:
        break;
    }
  };

  const loading = (
    <span className={styles.action}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );

  if (!initialState) {
    return loading;
  }

  const { currentUser } = initialState;

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  // 构建菜单项 - 只保留个人信息选项
  const buildMenuItems = () => {
    const items = [];

    if (menu) {
      // 个人信息选项
      items.push({
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人信息',
      });
    }

    return items;
  };

  const menuItems = buildMenuItems();

  return (
    <HeaderDropdown
      menu={{
        selectedKeys: [],
        onClick: onMenuClick,
        items: menuItems,
      }}
    >
      {children}
    </HeaderDropdown>
  );
};
